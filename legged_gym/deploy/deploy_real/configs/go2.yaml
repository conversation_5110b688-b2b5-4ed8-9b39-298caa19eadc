# 
control_dt: 0.02

msg_type: "go"     # "hg" or "go"
imu_type: "pelvis"    # "torso" or "pelvis"，h1 and h1_2 imu is on the torso

lowcmd_topic: "rt/lowcmd"
lowstate_topic: "rt/lowstate"

policy_path: "{LEGGED_GYM_ROOT_DIR}/deploy/pre_train/go2/policy.pt"

leg_joint2motor_idx: [3, 4, 5, 0, 1, 2, 9, 10, 11, 6, 7, 8]
kps: [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20]
kds: [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5]
default_angles: [0.1,  0.8,  -1.5,  -0.1, 0.8, -1.5, 
                  0.1,  1,  -1.5,  -0.1, 1, -1.5]

ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.785]
num_actions: 12
num_obs_current: 45
num_obs_encoder: 270
num_obs: 64

