import torch, torch.nn as nn, torch.optim as optim
import gymnasium as gym, numpy as np, tqdm

class Policy(nn.Module):
    def __init__(self, n_obs=4, n_act=2):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(n_obs, 64), nn.<PERSON>(),
            nn.Linear(64, n_act)
        )
    def forward(self, s):
        logits = self.net(s)
        return torch.distributions.Categorical(logits=logits)

env = gym.make('CartPole-v1')
pi = Policy()
opt = optim.Adam(pi.parameters(), 3e-4)

for epoch in tqdm.trange(200):
    states, actions, rewards, logp_old = [], [], [], []
    s, _ = env.reset()
    for t in range(400):
        s_t = torch.tensor(s, dtype=torch.float32)
        dist = pi(s_t)
        a = dist.sample()
        logp = dist.log_prob(a)
        s, r, done, _, _ = env.step(a.item())
        states.append(s_t); actions.append(a); logp_old.append(logp)
        rewards.append(r)
        if done: break
    # 计算回报与优势
    R = 0; returns = []
    for r in reversed(rewards):
        R = r + 0.99 * R; returns.insert(0, R)
    returns = torch.tensor(returns, dtype=torch.float32)
    adv = (returns - returns.mean()) / (returns.std() + 1e-8)
    # PPO 更新
    for _ in range(10):
        dist = pi(torch.stack(states))
        logp = dist.log_prob(torch.stack(actions))
        ratio = torch.exp(logp - torch.stack(logp_old))
        clip_adv = torch.clamp(ratio, 0.8, 1.2) * adv
        loss = -(torch.min(ratio * adv, clip_adv)).mean()
        opt.zero_grad(); loss.backward(); opt.step()
print("训练完成，平均回报：", np.sum(rewards))